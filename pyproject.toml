[project]
name = "agentlightning"
version = "0.1"
description = "Agent Lightning"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
  "graphviz",
  "psutil",
  "setproctitle",
  "flask",
  "agentops<=0.4.18",
  "httpdbg",
  "uvicorn",
  "fastapi",
]

[project.optional-dependencies]
dev = [
  "flake8",
  "pytest",
  "hatch",
  "pytest-asyncio",
  "pre-commit",
  "pytest-rerunfailures",
  "black",
]
experiment = [
  "random-word",
]
agent = [
  "autogen-agentchat",
  "autogen-ext[openai]",
  "litellm[proxy]",
  "mcp",
  "openai-agents",
  "langgraph",
  "langchain[openai]",
  "langchain-community",
  "langchain-text-splitters",
  "sqlparse",
  "nltk",
  "uv",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["agentlightning"]
include = ["**/*.yaml", "**/*.yml"]

[tool.pytest.ini_options]
testpaths = ["tests"]
