name: CPU Test

permissions:
  contents: read

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:

  schedule:
    # Every day at noon and midnight
    - cron: '0 0,12 * * *'

jobs:

  lint:
    name: Lint with Black
    runs-on: ubuntu-latest
    timeout-minutes: 10
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-python@v4
        with:
          python-version: '3.12'
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -e .[dev]
      - name: Run Black
        run: |
          black --check --diff --line-length=120 .

  test:
    strategy:
      matrix:
        include:
          - python-version: '3.10'
            setup-script: 'stable'
          - python-version: '3.12'
            setup-script: 'latest'
          - python-version: '3.12'
            setup-script: 'stable'

    name: Test with Python ${{ matrix.python-version }} (${{ matrix.setup-script }})
    runs-on: ubuntu-latest
    timeout-minutes: 15
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install dependencies
        run: |
          ./scripts/setup_${{ matrix.setup-script }}.sh
      - name: Freeze dependencies
        run: |
          pip list | tee requirements-freeze-${{ matrix.python-version }}-${{ matrix.setup-script }}.txt
      - name: Upload dependencies artifact
        uses: actions/upload-artifact@v4
        with:
          name: dependencies-python-${{ matrix.python-version }}-${{ matrix.setup-script }}
          path: requirements-freeze-${{ matrix.python-version }}-${{ matrix.setup-script }}.txt
          compression-level: 0
      - name: Run tests
        run: |
          pytest -v tests
        env:
          PYTEST_ADDOPTS: "--color=yes"
